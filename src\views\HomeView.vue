<template>
  <div class="home-container">
    <!-- 头部标题 -->
    <div class="header">
      <h1 class="title">信小财RPA财务机器人V2.3.5</h1>
      <div class="header-actions">
        <el-button type="primary" size="small">
          <el-icon>
            <Document />
          </el-icon>
          操作手册
        </el-button>
      </div>
    </div>

    <!-- 功能模块网格 -->
    <div class="modules-grid">
      <!-- 第一行 -->
      <div class="module-card" @click="navigateTo('/statistics')">
        <div class="module-icon statistics">
          <div class="icon-wrapper">
            <el-icon>
              <DataLine />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>统计大师</h3>
          <p>快速生成各类统计报表</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/auto-voucher')">
        <div class="module-icon auto-voucher">
          <div class="icon-wrapper">
            <el-icon>
              <Document />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>自动制证</h3>
          <p>一键完成各类凭证制作</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/fund-manager')">
        <div class="module-icon fund">
          <div class="icon-wrapper">
            <el-icon>
              <Wallet />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>资金协管</h3>
          <p>智能化资金管理专家</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/smart-audit')">
        <div class="module-icon audit">
          <div class="icon-wrapper">
            <el-icon>
              <Search />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>智能稽核</h3>
          <p>智能化单据审核验证</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/one-click-close')">
        <div class="module-icon close">
          <div class="icon-wrapper">
            <el-icon><Select /></el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>一键结账</h3>
          <p>快速完成月末结账处理</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/smart-tax')">
        <div class="module-icon tax">
          <div class="icon-wrapper">
            <el-icon>
              <Files />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>智慧税务</h3>
          <p>智能化税务处理专家</p>
        </div>
      </div>

      <!-- 第二行 -->
      <div class="module-card" @click="navigateTo('/quick-fill')">
        <div class="module-icon quick-fill">
          <div class="icon-wrapper">
            <el-icon>
              <Edit />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>速填精灵</h3>
          <p>精确快速的数据填充</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/report-assistant')">
        <div class="module-icon assistant">
          <div class="icon-wrapper">
            <el-icon>
              <Histogram />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>报表助手</h3>
          <p>生成各类分析报表</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/electronic-archive')">
        <div class="module-icon archive">
          <div class="icon-wrapper">
            <el-icon>
              <Folder />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>电子档案</h3>
          <p>电子文档管理系统</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/system-sync')">
        <div class="module-icon sync">
          <div class="icon-wrapper">
            <el-icon>
              <Link />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>系统协同</h3>
          <p>多系统协同工作平台</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/settings')">
        <div class="module-icon settings">
          <div class="icon-wrapper">
            <el-icon>
              <Tools />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>设置管理</h3>
          <p>用于参数设置及系统配置</p>
        </div>
      </div>

      <div class="module-card" @click="navigateTo('/others')">
        <div class="module-icon others">
          <div class="icon-wrapper">
            <el-icon>
              <Grid />
            </el-icon>
          </div>
        </div>
        <div class="module-info">
          <h3>其他</h3>
          <p>更多功能和工具</p>
        </div>
      </div>
    </div>

    <!-- 底部信息面板 -->
    <div class="info-panels">
      <div class="info-panel">
        <h4>
          <el-icon>
            <List />
          </el-icon>
          系统状态
        </h4>
        <ul>
          <li>RPA数据自动采集</li>
          <li>系统运行正常</li>
          <li>数据库连接正常</li>
        </ul>
      </div>

      <div class="info-panel">
        <h4>
          <el-icon>
            <InfoFilled />
          </el-icon>
          软件信息
        </h4>
        <div class="software-info">
          <p>当前版本：<span class="version">V2.3.5</span></p>
          <p>版本状态：<span class="status">正式版本</span></p>
          <p>运行环境：<span>7x</span></p>
        </div>
      </div>

      <div class="info-panel">
        <h4>
          <el-icon>
            <Calendar />
          </el-icon>
          最近活动
        </h4>
        <div class="recent-activity">
          <p>客户端启动</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateTo = (path) => {
  router.push(path)
}
</script>

<style scoped>
.home-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  box-sizing: border-box;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 10px;
  width: 100%;
  max-width: 1200px;
}

.title {
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  margin-bottom: 30px;
  width: 100%;
  max-width: 1200px;
  justify-items: center;
  place-content: center;
}

.module-card {
  background: white;
  border-radius: 16px;
  padding: 24px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  width: 100%;
  min-width: 160px;
  max-width: 180px;
  position: relative;
  overflow: hidden;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.module-card:hover::before {
  opacity: 1;
}

.module-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.module-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.icon-wrapper {
  font-size: 26px;
  color: white;
  z-index: 2;
  position: relative;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.module-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.module-icon:hover::before {
  opacity: 1;
}

/* 财务主题配色方案 */
.module-icon.statistics {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.module-icon.auto-voucher {
  background: linear-gradient(135deg, #4facfe 0%, #00c6ff 100%);
}

.module-icon.fund {
  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
}

.module-icon.audit {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.module-icon.close {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.module-icon.tax {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.module-icon.quick-fill {
  background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
}

.module-icon.assistant {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
}

.module-icon.archive {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.module-icon.sync {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.module-icon.settings {
  background: linear-gradient(135deg, #81ecec 0%, #00b894 100%);
}

.module-icon.others {
  background: linear-gradient(135deg, #a29bfe 0%, #74b9ff 100%);
}

.module-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #2c3e50;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.module-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.5;
  font-weight: 400;
}

.info-panels {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  width: 100%;
  max-width: 1200px;
}

.info-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.info-panel h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.info-panel ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-panel li {
  padding: 5px 0;
  color: #5a6c7d;
  font-size: 14px;
}

.software-info p {
  margin: 8px 0;
  color: #5a6c7d;
  font-size: 14px;
}

.version {
  color: #3498db;
  font-weight: 600;
}

.status {
  color: #27ae60;
  font-weight: 600;
}

.recent-activity p {
  margin: 8px 0;
  color: #5a6c7d;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .modules-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .modules-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-panels {
    grid-template-columns: 1fr;
  }
}
</style>