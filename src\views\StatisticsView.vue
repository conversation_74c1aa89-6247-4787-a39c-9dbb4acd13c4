<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon><ArrowLeft /></el-icon>
        返回主页
      </el-button>
      <h1 class="title">统计大师 - 数据分析中心</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <div 
          v-for="module in modules" 
          :key="module.key"
          :class="['nav-tab', { active: activeModule === module.key }]"
          @click="setActiveModule(module.key)"
        >
          <el-icon><component :is="module.icon" /></el-icon>
          <span>{{ module.title }}</span>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

// 导入现有的组件
import DataAnalysisView from './DataAnalysisView.vue'
import BudgetReportView from './BudgetReportView.vue'
import SalaryTax2View from './SalaryTax2View.vue'
import CapitalFlowView from './CapitalFlowView.vue'
import ProjectReportView from './ProjectReportView.vue'
import SamrtWorkerView from './SamrtWorkerView.vue'
import SmartReconciliationView from './SmartReconciliationView.vue'
import QuickQueryView from './QuickQueryView.vue'
import QuickFillView from './QuickFillView.vue'
import { defineAsyncComponent } from 'vue'

const VoucherQueryView = defineAsyncComponent(() => import('./VoucherQueryView.vue'))

const router = useRouter()
const activeModule = ref('data-analysis')

const modules = [
  {
    key: 'data-analysis',
    title: '我的账面',
    icon: 'DataAnalysis',
    component: DataAnalysisView
  },
  {
    key: 'budget-report',
    title: '报表快算',
    icon: 'TrendCharts',
    component: BudgetReportView
  },
  {
    key: 'salary-tax',
    title: '薪酬个税',
    icon: 'Money',
    component: SalaryTax2View
  },
  {
    key: 'capital-flow',
    title: '资金流动',
    icon: 'SwitchButton',
    component: CapitalFlowView
  },
  {
    key: 'project-report',
    title: '项目台账',
    icon: 'Folder',
    component: ProjectReportView
  },
  {
    key: 'smart-worker',
    title: '工人专区',
    icon: 'User',
    component: SamrtWorkerView
  },
  {
    key: 'smart-reconciliation',
    title: '对账抵消',
    icon: 'Files',
    component: SmartReconciliationView
  },
  {
    key: 'voucher-query',
    title: '凭证查询',
    icon: 'Document',
    component: VoucherQueryView
  },
  {
    key: 'quick-query',
    title: '快速查询',
    icon: 'Search',
    component: QuickQueryView
  },
  {
    key: 'quick-fill',
    title: '速填模板',
    icon: 'EditPen',
    component: QuickFillView
  }
]

const currentComponent = computed(() => {
  const module = modules.find(m => m.key === activeModule.value)
  return module ? module.component : DataAnalysisView
})

const setActiveModule = (key) => {
  activeModule.value = key
}

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.statistics-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  overflow: hidden;
  background: #f5f7fa;
}

.content-area > * {
  height: 100%;
  overflow: auto;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.nav-tabs::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>