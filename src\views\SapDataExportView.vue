<template>
  <div class="sap-export-container">
    <div class="export-form">
      <h2>SAP数据导出</h2>
      <el-form :model="form" label-width="120px">
        <el-form-item label="开始日期">
          <el-date-picker v-model="form.startDate" type="date" placeholder="选择开始日期" value-format="YYYY-MM-DD"
            format="YYYY-MM-DD" />
        </el-form-item>

        <el-form-item label="结束日期">
          <el-date-picker v-model="form.endDate" type="date" placeholder="选择结束日期" value-format="YYYY-MM-DD"
            format="YYYY-MM-DD" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="exportData" :loading="loading">
            {{ loading ? '导出中...' : '导出数据' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const form = ref({
  startDate: '',
  endDate: ''
})

const loading = ref(false)
const dialogVisible = ref(false)

const exportData = async () => {
  if (!form.value.startDate || !form.value.endDate) {
    ElMessage.warning('请选择开始日期和结束日期')
    return
  }

  if (form.value.startDate > form.value.endDate) {
    ElMessage.warning('开始日期不能晚于结束日期')
    return
  }

  loading.value = true

  try {
    // 发送POST请求导出数据
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': 'sap明细数据导出',
        '参数': [form.value.startDate, form.value.endDate]
      })
    })

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('数据导出任务已启动')
      // 这里可以添加下载文件的逻辑
    } else {
      ElMessage.error('数据导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.sap-export-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
  position: relative;
}

.export-form {
  max-width: 500px;
  margin: 0 auto;
}

.export-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}
</style>