<template>
  <div class="salary-tax2-view">
    <!-- Compact header -->
    <div class="compact-header">
      <div class="header-content">
        <h1 class="page-title">
          <span class="title-icon">💼</span>
          薪酬个税管理系统
        </h1>
      </div>
      <div class="header-controls">
        <select v-model="selectedYear" class="year-select">
          <option value="">请选择年份</option>
          <option value="2024">2024年</option>
          <option value="2025">2025年</option>
          <option value="2026">2026年</option>
          <option value="2027">2027年</option>
        </select>
        <button @click="fetchAllData" class="fetch-button" :disabled="!selectedYear || isLoading">
          <span class="button-icon">📥</span>
          <span>拉取数据</span>
        </button>
        <button @click="saveAllData" class="save-button" :disabled="isLoading">
          <span class="button-icon">💾</span>
          <span>保存数据</span>
        </button>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="action-buttons">
      <button @click="matchIdAndProject" class="action-button match">
        <span class="button-icon">🔗</span>
        <span>匹配身份证号及项目</span>
      </button>
      <button @click="calculateTax" class="action-button tax">
        <span class="button-icon">🧮</span>
        <span>计算个税</span>
      </button>
      <button @click="generateDeclaration" class="action-button declaration">
        <span class="button-icon">📄</span>
        <span>生成申报表</span>
      </button>
      <button @click="generateDivision" class="action-button declaration">
        <span class="button-icon">📑</span>
        <span>生成劳务派遣分割表</span>
      </button>
      <button @click="pushDeclaration" class="action-button push">
        <span class="button-icon">🚀</span>
        <span>构建财务一体化计提发放模板(薪酬发放表)</span>
      </button>
      <button @click="buildFinanceTemplate" class="action-button template">
        <span class="button-icon">🏗️</span>
        <span>构建财务一体化计提发放模板(社保公积金表)</span>
      </button>
      <button @click="calculateSocialSecurityAllocation" class="action-button export">
        <span class="button-icon">📤</span>
        <span>计算社保公积金回摊</span>
      </button>
      <button @click="exportAllTablesToExcel" class="action-button export-all">
        <span class="button-icon">💾</span>
        <span>导出所有表格</span>
      </button>
      <button @click="triggerIncomeImport" class="action-button import">
        <span class="button-icon">📊</span>
        <span>导入收入</span>
      </button>
      <button @click="triggerSpecialDeductionImport" class="action-button import">
        <span class="button-icon">📥</span>
        <span>导入专项附加</span>
      </button>
      <input ref="fileInput" type="file" accept=".xlsx,.xls" style="display: none"
        @change="handleSpecialDeductionImport" />
      <input ref="incomeFileInput" type="file" accept=".xlsx,.xls" style="display: none" @change="handleIncomeImport" />
    </div>

    <!-- Loading indicator -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">处理中...</div>
    </div>

    <!-- Univer container -->
    <div class="univer-container" :class="{ 'loading': isLoading }">
      <div ref="univerContainer" class="univer-spreadsheet"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { createUniver, defaultTheme, LocaleType, merge, Univer, FUniver } from '@univerjs/presets'
import { UniverSheetsCorePreset } from '@univerjs/presets/preset-sheets-core'
import UniverPresetSheetsCoreZhCN from '@univerjs/presets/preset-sheets-core/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-core.css'

import { UniverSheetsConditionalFormattingPreset } from '@univerjs/presets/preset-sheets-conditional-formatting'
import sheetsConditionalFormattingZhCN from '@univerjs/presets/preset-sheets-conditional-formatting/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-conditional-formatting.css'

import { UniverSheetsDataValidationPreset } from '@univerjs/presets/preset-sheets-data-validation'
import sheetsDataValidationZhCN from '@univerjs/presets/preset-sheets-data-validation/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-data-validation.css'

import { UniverSheetsFilterPreset } from '@univerjs/presets/preset-sheets-filter'
import sheetsFilterZhCN from '@univerjs/presets/preset-sheets-filter/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-filter.css'

import { UniverSheetsTablePlugin } from '@univerjs/sheets-table'
import { UniverSheetsTableUIPlugin } from '@univerjs/sheets-table-ui'
import SheetsTableUIZhCN from '@univerjs/sheets-table-ui/locale/zh-CN'

import '@univerjs/sheets-table-ui/lib/index.css'
import '@univerjs/sheets-table/facade'

import * as ExcelJS from 'exceljs'
import taxCalculatorTs from '../scripts/taxCalculator'
// 响应式数据
const univerContainer = ref<HTMLElement | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)
const incomeFileInput = ref<HTMLInputElement | null>(null)
let univerInstance: Univer | null = null
let univerAPIInstance: FUniver | null = null
const isLoading = ref(false)
const activeTabIndex = ref(0)
const undoStack = ref([])
const lastUpdateTime = ref('')
const selectedYear = ref('')

// 标签页配置
const tabs = ref([
  { name: '个税申报表', key: 'taxDeclaration' },
  { name: '算税底稿', key: 'taxCalculation' },
  { name: '包干费', key: 'subsidyPackage' },
  { name: '一次性年终奖', key: 'bonus' },
  { name: '外包薪酬表', key: 'outsourcingSalary' },
  { name: '社保公积金实际缴纳表', key: 'socialSecurityAndHousingFund' },
  { name: '薪酬总表(无一次性年终)', key: 'socialSecurityAndHousingFundAdjustment' },
  { name: '累计专项附加扣除表', key: 'specialDeduction' },
  { name: '异地纳税', key: 'remoteTax' },
  { name: '身份证号表', key: 'idCard' },
  { name: '项目匹配表', key: 'projectMapping' },
  { name: '配置表', key: 'incomeHeaderMapping' }
])

// 默认初始化数据
const initializeDefaultData = () => {
  return {
    taxDeclaration: [
      [
        '工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
        '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
        '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
        '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
        '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
        '准予扣除的捐赠额', '减免税额', '备注'
      ],
      [
        'E001', '张三', '身份证', '110101199001011234', 15000, 0,
        800, 240, 80, 960, 1000, 0, 1000, 0, 2000, 0, 0, 0, 0, 0, 0, 0, 0, ''
      ],
      [
        'E002', '李四', '身份证', '110101199102022345', 12000, 0,
        750, 200, 60, 900, 1000, 400, 0, 1500, 2000, 0, 0, 0, 0, 0, 0, 0, 0, ''
      ]
    ],
    taxCalculation: [
      [
        '月份', '算税地区', '社保缴纳单位', '公积金缴纳单位', '年金缴纳单位', '身份证号', '姓名', '成本所属项目', '财务标准项目', '财务标准单位', '薪酬类别',
        '本期收入', '基本养老保险费', '住房公积金', '基本医疗保险费',
        '失业保险费', '企业(职业)年金', '其它扣款', '调整收入', '调整扣除',
        '调整累计个税', '累计社保', '累计专项附加', '累计法定扣除',
        '累计调整扣除', '累计收入', '累计扣除', '累计应扣税款',
        '累计上次税款', '本次税款', '本次达到税率', '一次性年终奖校验', '备注'
      ],
      [
        1, '北京市(注意旁边月份为整数)', '华南', '武汉1', '美国', '110101199001011234', '张三', '研发项目A', '财务一体化项目1', '财务一体化单位1', '工资',
        15000, 800, 960, 240, 80, 0, 0, 0, 0, 0, 2080, 4000, 5000, 0,
        15000, 11080, 595, 0, 595, '10%', '否', '备注1'
      ]
    ],
    specialDeduction: [
      [
        '工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止',
        '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息',
        '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金'
      ],
      [
        'E001', '张三', '身份证', '110101199001011234', '2025-01-01', '2025-01-31',
        15000, 1000, 0, 1000, 0, 2000, 0, 0
      ]
    ],
    remoteTax: [
      [
        '预留', '身份证号', '姓名', '一月', '二月', '三月', '四月', '五月', '六月',
        '七月', '八月', '九月', '十月', '十一月', '十二月'
      ],
      [
        '', '110101199001011234', '张三', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
      ]
    ],
    idCard: [
      ['姓名', '身份证号', '银行卡号', '开户行名称', '开户行编码', '备注'],
      ['张三', '110101199001011234', '6217000010000000000', '中国银行', '9999999999999999999', '备注1'],
      ['李四', '110101199102022345', '6217000020000000000', '中国银行', '9999999999999999999', '备注2']
    ],
    projectMapping: [
      ['人资项目名称', '标准项目名称', '标准项目编码', '财务标准单位', '财务标准单位编码'],
      ['人资项目1', '财务一体化项目1', '12211235664', '财务一体化单位1', '财务一体化单位编码1']
    ],
    incomeHeaderMapping: [
      ['Excel标题', '目标标题', '备注', '', '', '', '', '其他配置', '配置内容'],
      ['工号', '工号', '员工工号', '', '', '', '', '公积金最大限额', 6000],
      ['姓名', '姓名', '员工姓名'],
      ['身份证号', '身份证号', '身份证号码'],
      ['身份证号码', '身份证号', '身份证号码'],
      ['证件号码', '身份证号', '证件号码'],
      ['本期收入', '本期收入', '当期收入金额'],
      ['收入', '本期收入', '收入金额'],
      ['工资', '本期收入', '工资收入'],
      ['薪酬', '本期收入', '薪酬收入'],
      ['基本养老保险费', '基本养老保险费', '养老保险'],
      ['养老保险', '基本养老保险费', '养老保险费'],
      ['基本医疗保险费', '基本医疗保险费', '医疗保险'],
      ['医疗保险', '基本医疗保险费', '医疗保险费'],
      ['失业保险费', '失业保险费', '失业保险'],
      ['失业保险', '失业保险费', '失业保险费'],
      ['住房公积金', '住房公积金', '公积金'],
      ['公积金', '住房公积金', '住房公积金'],
      ['企业年金', '企业(职业)年金', '企业年金'],
      ['职业年金', '企业(职业)年金', '职业年金'],
      ['月份', '月份', '所属月份'],
      ['算税地区', '算税地区', '纳税地区'],
      ['纳税地区', '算税地区', '纳税地区'],
      ['成本所属项目', '成本所属项目', '项目名称'],
      ['项目名称', '成本所属项目', '项目名称'],
      ['财务标准项目', '财务标准项目', '标准项目'],
      ['财务标准单位', '财务标准单位', '标准单位'],
      ['薪酬类别', '薪酬类别', '薪酬类型']
    ],
    subsidyPackage: [
      ['月份', '身份证号', '姓名', '成本所属项目', '财务标准项目', '工作餐补贴', '交通补贴', '通讯补贴', '住房补贴', '取证补贴', '其他补贴', '合计补贴', '备注'],
      [1, '110101199001011234', '张三', '研发项目A', '财务一体化项目1', 15000, 800, 960, 240, 80, 80, 80, "备注1"]
    ],
    bonus: [
      ['缴纳月份', '算税地区', '身份证号', '姓名', '成本所属项目', '财务标准项目', '薪酬类别', '原奖金金额', '报税收入', '税额', '税率', '备注'],
      [1, '北京市', '110101199001011234', '张三', '研发项目A', '财务一体化项目1', '商务兑现', 15000, 15000, 595, '10%', '备注1']
    ],
    outsourcingSalary: [
      ['月份', '外包公司', '姓名', '身份证号', '人资项目', '财务标准项目', '总金额', '进项税', '入账成本', '薪酬类别', '备注'],
      [1, '外包公司1', '张三', '110101199001011234', '人资项目1', '财务标准项目1', 15000, 595, 15000, '工资', '备注1']
    ],
    socialSecurityAndHousingFund: [
      ['月份', '代缴单位', '个人公积金', '个人养老', '个人医疗', '个人失业', '个人年金', '补充医疗', '单位公积金', '单位养老', '单位医疗', '单位失业', '单位工伤', '单位生育', '企业年金', '合计金额', '备注'],
      [1, '代缴单位1', 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, '备注1']
    ],
    socialSecurityAndHousingFundAdjustment: [
      [
        '月份', '社保缴纳单位', '公积金缴纳单位', '年金缴纳单位', '身份证号', '姓名', '成本所属项目', '财务标准项目', '财务标准单位', '薪酬类别',
        '本期收入', '个人养老回摊', '个人公积金回摊', '个人医疗回摊',
        '个人失业回摊', '个人年金回摊', '其它扣款', '包干费补贴', '本次个税',
        '单位养老分摊', '单位公积金分摊', '单位医疗分摊', '单位失业分摊',
        '单位工伤分摊', '单位生育分摊', '补充医疗分摊', '其他分摊',
        '总成本', '备注'
      ],
      [
        1, '华南', '武汉1', '美国', '110101199001011234', '张三', '研发项目A', '财务一体化项目1', '财务一体化单位1', '工资',
        15000, 800, 960, 240, 80, 0, 0, 0, 0,
        0, 2080, 4000, 5000, 0, 15000, 11080, 1, 1, '备注1'
      ]
    ]
  }
}

// 处理身份证号和证件号码列为文本格式 - 使用Univer字典对象格式
const processIdColumnsAsText = (data: any[][]): any[][] => {
  if (!data || data.length === 0) return data

  // 获取表头行
  const headerRow = data[0]
  if (!headerRow) return data

  // 找出身份证号和证件号码列的索引
  const idColumns: number[] = []
  headerRow.forEach((header, index) => {
    if (header && typeof header === 'string') {
      const headerStr = header.trim()
      if (headerStr.includes('身份证号') || headerStr.includes('证件号码') || headerStr.includes('银行卡号')) {
        idColumns.push(index)
      }
    }
  })

  // 如果没有找到ID列，直接返回原数据
  if (idColumns.length === 0) return data

  // 处理数据，确保ID列为文本格式 - 使用Univer字典对象格式
  return data.map((row, rowIndex) => {
    if (rowIndex === 0) return row // 保持表头不变

    return row.map((cell, colIndex) => {
      if (idColumns.includes(colIndex) && cell) {
        const cellValue = String(cell).trim()
        // 对于身份证号、证件号码、银行卡号等，使用Univer字典对象格式强制为文本
        if (/^\d+$/.test(cellValue)) {
          return {
            v: cellValue,
            t: 1, // CellValueType.STRING
            s: null
          }
        }
        // 如果已经是字符串但包含数字，也转换为字典格式
        return {
          v: cellValue,
          t: 1, // CellValueType.STRING  
          s: null
        }
      }
      return cell
    })
  })
}

// 数据存储
const tabData = ref(initializeDefaultData())

// 初始化Univer
const initUniver = async () => {
  if (!univerContainer.value) return

  try {
    const { univer, univerAPI } = createUniver({
      locale: LocaleType.ZH_CN,
      locales: {
        [LocaleType.ZH_CN]: merge(
          {},
          UniverPresetSheetsCoreZhCN,
          sheetsConditionalFormattingZhCN,
          sheetsDataValidationZhCN,
          sheetsFilterZhCN,
          SheetsTableUIZhCN,
        )
      },
      theme: defaultTheme,
      presets: [
        UniverSheetsCorePreset({
          container: univerContainer.value as HTMLElement
        }),
        UniverSheetsConditionalFormattingPreset(),
        UniverSheetsDataValidationPreset(),
        UniverSheetsFilterPreset()
      ]
    })

    univer.registerPlugin(UniverSheetsTablePlugin)
    univer.registerPlugin(UniverSheetsTableUIPlugin)

    univerInstance = univer
    univerAPIInstance = univerAPI

    // 创建工作簿
    const workbook = univerAPIInstance.createWorkbook({
      id: 'salary-tax-workbook',
      name: '薪酬个税管理'
    })

    // 为每个标签页创建工作表并加载初始数据
    tabs.value.forEach((tab, index) => {
      const sheet = workbook.create(tab.name, 100, 50) // 增加默认列数

      // 加载初始数据
      const initialData = tabData.value[tab.key]
      if (initialData && initialData.length > 0) {
        // 自动调整工作表大小以适应数据
        const requiredRows = Math.max(initialData.length + 1, 2)
        const requiredCols = Math.max(initialData[0].length + 1, 8)

        // 更新工作表大小
        sheet.setRowCount(requiredRows)
        sheet.setColumnCount(requiredCols)

        // 处理身份证号和证件号码列的文本格式
        const processedData = processIdColumnsAsText(initialData)

        // 设置数据
        sheet.getRange(0, 0, processedData.length, processedData[0].length).setValues(processedData)

        // 设置表头样式
        if (initialData.length > 0) {
          const headerRange = sheet.getRange(0, 0, 1, initialData[0].length)
          headerRange.setFontWeight('bold')
          headerRange.setBackgroundColor('#f0f0f0')
        }
      }

      if (index === 0) {
        // 设置默认激活第一个工作表
        workbook.setActiveSheet(sheet.getSheetId())
      }
    })

    // 删除默认的Sheet1（如果存在）
    try {
      workbook.deleteSheet(workbook.getSheetByName("Sheet1"))
    } catch (error) {
      console.warn('删除Sheet1失败:', error)
    }

    console.log('Univer初始化成功，已加载所有初始数据')
  } catch (error) {
    console.error('Univer初始化失败:', error)
  }
}

// 切换标签页（移除，因为Univer自带tab功能）
// const switchTab = (index: number) => {
//   if (!univerAPIInstance) return
//   
//   activeTabIndex.value = index
//   const workbook = univerAPIInstance.getActiveWorkbook()
//   if (workbook) {
//     const sheets = workbook.getSheets()
//     if (sheets[index]) {
//       workbook.setActiveSheet(sheets[index].getSheetId())
//     }
//   }
// }

// 加载数据到当前工作表（支持自动扩展行列）
const loadDataToCurrentSheet = (data: any[][]) => {
  if (!univerAPIInstance || !data.length) return

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const activeSheet = workbook?.getActiveSheet()

    if (activeSheet && data.length > 0) {
      // 清除现有数据
      activeSheet.clear()

      // 自动调整工作表大小以适应数据
      const requiredRows = Math.max(data.length + 10, 100)
      const requiredCols = Math.max(data[0].length + 5, 50)

      // 更新工作表大小
      activeSheet.setRowCount(requiredRows)
      activeSheet.setColumnCount(requiredCols)

      // 处理身份证号和证件号码列的文本格式
      const processedData = processIdColumnsAsText(data)

      // 设置数据
      activeSheet.getRange(0, 0, processedData.length, processedData[0].length).setValues(processedData)

      // 设置表头样式
      if (data.length > 0) {
        const headerRange = activeSheet.getRange(0, 0, 1, data[0].length)
        headerRange.setFontWeight('bold')
        headerRange.setBackgroundColor('#f0f0f0')
      }

      // 更新时间戳
      lastUpdateTime.value = new Date().toLocaleTimeString()
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

// 加载数据到指定工作表（支持自动扩展行列）
const loadDataToSheet = (sheetIndex: number, data: any[][]) => {
  if (!univerAPIInstance) return

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const sheets = workbook?.getSheets()

    if (sheets && sheets[sheetIndex]) {
      const sheet = sheets[sheetIndex]

      // 如果有数据，则设置数据
      if (data.length > 0) {
        // 处理身份证号和证件号码列的文本格式
        let processedData = processIdColumnsAsText(data)

        // 校验每一行长度与表头一致
        const colCount = processedData[0]?.length || 0
        processedData = processedData.map(row => {
          const newRow = Array.from(row)
          if (newRow.length < colCount) {
            // 补齐
            return newRow.concat(Array(colCount - newRow.length).fill(''))
          } else if (newRow.length > colCount) {
            // 截断
            return newRow.slice(0, colCount)
          }
          return newRow
        })

        // 自动调整工作表大小以适应数据
        const requiredRows = Math.max(processedData.length + 10, 100)
        const requiredCols = Math.max(colCount + 5, 50)
        sheet.setRowCount(requiredRows)
        sheet.setColumnCount(requiredCols)

        // 设置数据
        sheet.getRange(0, 0, processedData.length, colCount).setValues(processedData)

        // 设置表头样式
        const headerRange = sheet.getRange(0, 0, 1, colCount)
        headerRange.setFontWeight('bold')
        headerRange.setBackgroundColor('#f0f0f0')
      } else {
        // 没有数据时，设置默认大小
        sheet.setRowCount(100)
        sheet.setColumnCount(50)
      }

      // 响应式赋值，确保UI同步
      const tabKey = tabs.value[sheetIndex]?.key
      if (tabKey) {
        tabData.value = { ...tabData.value, [tabKey]: data }
      }
    }
  } catch (error) {
    console.error('加载数据到工作表失败:', error)
  }
}

// 从Excel文件导入专项附加扣除数据
const importSpecialDeductionFromExcel = async (file: File, importMode: 'overwrite' | 'append', worksheetName?: string) => {
  try {
    isLoading.value = true

    const workbook = new ExcelJS.Workbook()
    const arrayBuffer = await file.arrayBuffer()
    await workbook.xlsx.load(arrayBuffer)

    // 获取指定工作表的数据
    const worksheet = worksheetName ? workbook.getWorksheet(worksheetName) : workbook.getWorksheet(1)
    if (!worksheet) {
      alert(`Excel文件中没有找到工作表${worksheetName ? ': ' + worksheetName : ''}！`)
      return
    }
    const idcardData = getSheetDataByName("身份证号表")
    const data: any[][] = []
    const headerRow: any[] = []
    let idColumns: number[] = []

    worksheet.eachRow((row, rowNumber) => {
      const rowData: any[] = []

      // 处理表头行，识别身份证号和证件号码列
      if (rowNumber === 1) {
        row.eachCell((cell, colNumber) => {
          const cellValue = cell.value ? String(cell.value).trim() : ''
          headerRow[colNumber - 1] = cellValue

          // 检查是否为身份证号或证件号码列
          if (cellValue.includes('身份证号') || cellValue.includes('证件号码')) {
            idColumns.push(colNumber - 1)
          }
        })
      }

      // 处理数据行
      row.eachCell((cell, colNumber) => {
        let cellValue = cell.value

        // 如果是身份证号或证件号码列，强制为文本格式
        if (idColumns.includes(colNumber - 1) && cellValue) {
          cellValue = String(cellValue).trim()
          // 确保是文本格式，防止Excel自动转换为数字
          if (/^\d+$/.test(cellValue)) {
            cellValue = "'" + cellValue
          }
        }

        rowData[colNumber - 1] = cellValue
      })

      data.push(rowData)
    })

    if (data.length > 0) {
      // 找到专项附加扣除表工作表
      const workbook = univerAPIInstance?.getActiveWorkbook()
      const sheets = workbook?.getSheets()
      const specialSheet = sheets?.find(sheet => sheet.getSheet().getName() === '累计专项附加扣除表')

      if (!specialSheet) {
        alert('未找到"累计专项附加扣除表"工作表！')
        return
      }

      let finalData: any[][]

      if (importMode === 'overwrite') {
        // 覆盖导入：直接使用新数据
        finalData = data
      } else {
        // 追加导入：获取现有数据并追加
        const maxRow = specialSheet.getMaxRows()
        const maxCol = specialSheet.getMaxColumns()
        const existingData = maxRow > 0 && maxCol > 0 ? specialSheet.getRange(0, 0, maxRow, maxCol).getValues() : []

        if (existingData.length > 0) {
          // 保留表头，追加数据行
          finalData = [...existingData]
          finalData.push(...data.slice(1)) // 跳过导入数据的表头
        } else {
          finalData = data
        }
      }

      // 处理身份证号列为文本格式
      const processedData = processIdColumnsAsText(finalData)

      // 更新工作表
      const requiredRows = Math.max(processedData.length + 10, 100)
      const requiredCols = Math.max(processedData[0]?.length + 5 || 0, 50)
      specialSheet.setRowCount(requiredRows)
      specialSheet.setColumnCount(requiredCols)

      // 清除现有数据并设置新数据
      specialSheet.clear()
      if (processedData.length > 0) {
        specialSheet.getRange(0, 0, processedData.length, processedData[0].length).setValues(processedData)

        // 设置表头样式
        const headerRange = specialSheet.getRange(0, 0, 1, processedData[0].length)
        headerRange.setFontWeight('bold')
        headerRange.setBackgroundColor('#f0f0f0')
      }

      // 更新本地数据
      tabData.value.specialDeduction = processedData

      const modeText = importMode === 'overwrite' ? '覆盖' : '追加'
      alert(`成功${modeText}导入专项附加扣除数据 ${processedData.length - 1} 行！`)
    } else {
      alert('Excel文件中没有数据！')
    }
  } catch (error) {
    console.error('导入专项附加扣除失败:', error)
    alert('导入专项附加扣除失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 计算社保公积金回摊
const calculateSocialSecurityAllocation = async () => {
  try {
    isLoading.value = true

    // 获取所有需要的工作表数据
    const workbook = univerAPIInstance?.getActiveWorkbook()
    const sheets = workbook?.getSheets()

    if (!sheets) {
      alert('未找到工作簿！')
      return
    }

    // 获取算税底稿数据
    const taxSheet = sheets.find(sheet => sheet.getSheet().getName() === '算税底稿')
    if (!taxSheet) {
      alert('未找到"算税底稿"工作表！')
      return
    }
    const taxMaxRow = taxSheet.getMaxRows()
    const taxMaxCol = taxSheet.getMaxColumns()
    const taxData = taxMaxRow > 0 && taxMaxCol > 0 ? taxSheet.getRange(0, 0, taxMaxRow, taxMaxCol).getValues() : []

    // 获取社保公积金实际缴纳表数据
    const socialSheet = sheets.find(sheet => sheet.getSheet().getName() === '社保公积金实际缴纳表')
    if (!socialSheet) {
      alert('未找到"社保公积金实际缴纳表"工作表！')
      return
    }
    const socialMaxRow = socialSheet.getMaxRows()
    const socialMaxCol = socialSheet.getMaxColumns()
    const socialData = socialMaxRow > 0 && socialMaxCol > 0 ? socialSheet.getRange(0, 0, socialMaxRow, socialMaxCol).getValues() : []

    if (!taxData || taxData.length === 0) {
      alert('算税底稿数据为空！')
      return
    }

    if (!socialData || socialData.length === 0) {
      alert('社保公积金实际缴纳表数据为空！')
      return
    }

    // 发送数据到后台计算回摊
    const response = await fetch('http://localhost:8000/api/calculate-social-security-allocation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taxCalculationData: taxData,
        socialSecurityData: socialData,
        year: selectedYear.value
      })
    })

    if (!response.ok) {
      throw new Error(`计算回摊失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()

    if (result.code === 200 && result.data) {
      // 更新薪酬总表(无一次性年终)工作表
      const adjustmentSheet = sheets.find(sheet => sheet.getSheet().getName() === '薪酬总表(无一次性年终)')
      if (adjustmentSheet) {
        const allocationData = result.data

        // 处理身份证号列为文本格式
        const processedData = processIdColumnsAsText(allocationData)

        // 更新工作表
        const requiredRows = Math.max(processedData.length + 10, 100)
        const requiredCols = Math.max(processedData[0]?.length + 5 || 0, 50)
        adjustmentSheet.setRowCount(requiredRows)
        adjustmentSheet.setColumnCount(requiredCols)

        // 清除现有数据并设置新数据
        adjustmentSheet.clear()
        if (processedData.length > 0) {
          adjustmentSheet.getRange(0, 0, processedData.length, processedData[0].length).setValues(processedData)

          // 设置表头样式
          const headerRange = adjustmentSheet.getRange(0, 0, 1, processedData[0].length)
          headerRange.setFontWeight('bold')
          headerRange.setBackgroundColor('#f0f0f0')
        }

        // 更新本地数据
        tabData.value.socialSecurityAndHousingFundAdjustment = processedData

        alert(`社保公积金回摊计算完成！共处理 ${processedData.length - 1} 条记录`)
      } else {
        alert('未找到"薪酬总表(无一次性年终)"工作表！')
      }
    } else {
      alert('计算回摊失败: ' + (result.message || '未知错误'))
    }
  } catch (error) {
    console.error('计算社保公积金回摊失败:', error)
    alert('计算社保公积金回摊失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 获取当前工作表数据
const getCurrentSheetData = (): any[][] => {
  if (!univerAPIInstance) return []

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const activeSheet = workbook?.getActiveSheet()

    if (activeSheet) {
      // 获取工作表的所有数据
      const maxRow = activeSheet.getMaxRows()
      const maxCol = activeSheet.getMaxColumns()

      if (maxRow > 0 && maxCol > 0) {
        const range = activeSheet.getRange(0, 0, maxRow, maxCol)
        return range.getValues()
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }

  return []
}

// 从算税底稿数据中提取月份和薪酬类别
const extractMonthsAndCategories = (data: any[][]) => {
  if (!data || data.length < 2) {
    return { months: [], salaryCategories: [], monthCategoryMap: {} }
  }

  const header = data[0]
  const monthIdx = header.indexOf('月份')
  const categoryIdx = header.indexOf('薪酬类别')

  if (monthIdx === -1 || categoryIdx === -1) {
    return { months: [], salaryCategories: [], monthCategoryMap: {} }
  }

  const monthsSet = new Set<number>()
  const categoriesSet = new Set<string>()
  const monthCategoryMap: Record<string, Set<string>> = {}

  // 从数据行中提取唯一的月份和薪酬类别，建立月份与薪酬类别的映射关系
  for (let i = 1; i < data.length; i++) {
    const row = data[i]
    if (row && row.length > Math.max(monthIdx, categoryIdx)) {
      const monthValue = row[monthIdx]
      const category = row[categoryIdx]

      // 确保月份是正整数
      if (monthValue !== null && monthValue !== undefined && monthValue !== '') {
        const month = Number(monthValue)
        if (Number.isInteger(month) && month > 0) {
          monthsSet.add(month)
          const monthStr = String(month)

          if (category !== null && category !== undefined && category !== '') {
            const categoryStr = String(category).trim()
            categoriesSet.add(categoryStr)

            // 建立月份与薪酬类别的映射
            if (!monthCategoryMap[monthStr]) {
              monthCategoryMap[monthStr] = new Set<string>()
            }
            monthCategoryMap[monthStr].add(categoryStr)
          }
        }
      }
    }
  }

  // 转换Set为Array并排序
  const monthCategoryMapFinal: Record<string, string[]> = {}
  Object.keys(monthCategoryMap).forEach(month => {
    monthCategoryMapFinal[month] = Array.from(monthCategoryMap[month]).sort()
  })

  return {
    months: Array.from(monthsSet).sort((a, b) => a - b).map(String),
    salaryCategories: Array.from(categoriesSet).sort(),
    monthCategoryMap: monthCategoryMapFinal
  }
}

// 显示选择对话框
const showDeclarationDialog = (months: string[], salaryCategories: string[], monthCategoryMap: Record<string, string[]>): Promise<{ months: string[], categories: string[] } | null> => {
  return new Promise((resolve) => {
    // 创建对话框HTML
    const dialogHtml = `
      <div id="declaration-dialog" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      ">
        <div style="
          background: white;
          padding: 30px;
          border-radius: 8px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
          max-width: 600px;
          width: 90%;
          max-height: 80vh;
          overflow-y: auto;
        ">
          <h3 style="margin-top: 0; color: #333; text-align: center;">推送申报设置</h3>
          
          <div style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #555;">选择月份：</label>
            <select id="month-select" style="
              width: 100%;
              padding: 8px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 14px;
            ">
              <option value="">请选择月份</option>
              ${months.map(month => `<option value="${month}">${month}月</option>`).join('')}
            </select>
          </div>
          
          <div style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #555;">选择薪酬类别（可多选）：</label>
            <div id="category-notice" style="
              color: #666;
              font-size: 12px;
              margin-bottom: 8px;
              font-style: italic;
            ">
              请先选择月份以显示对应的薪酬类别
            </div>
            <div id="category-checkboxes" style="
              max-height: 200px;
              overflow-y: auto;
              border: 1px solid #ddd;
              border-radius: 4px;
              padding: 10px;
              background: #f9f9f9;
              min-height: 100px;
            ">
              <!-- 薪酬类别选项将根据月份动态生成 -->
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 25px;">
            <button id="confirm-btn" style="
              background: #4CAF50;
              color: white;
              border: none;
              padding: 10px 20px;
              margin-right: 10px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
              transition: background-color 0.3s;
            " onmouseover="this.style.backgroundColor='#45a049'" onmouseout="this.style.backgroundColor='#4CAF50'">
              确认推送
            </button>
            <button id="cancel-btn" style="
              background: #f44336;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
              transition: background-color 0.3s;
            " onmouseover="this.style.backgroundColor='#da190b'" onmouseout="this.style.backgroundColor='#f44336'">
              取消
            </button>
          </div>
        </div>
      </div>
    `

    // 添加对话框到页面
    document.body.insertAdjacentHTML('beforeend', dialogHtml)

    const dialog = document.getElementById('declaration-dialog')
    const monthSelect = document.getElementById('month-select') as HTMLSelectElement
    const categoryCheckboxes = document.getElementById('category-checkboxes')
    const categoryNotice = document.getElementById('category-notice')
    const confirmBtn = document.getElementById('confirm-btn')
    const cancelBtn = document.getElementById('cancel-btn')

    // 更新薪酬类别选项的函数
    const updateCategoryOptions = (selectedMonth: string) => {
      if (!categoryCheckboxes || !categoryNotice) return

      if (!selectedMonth) {
        categoryCheckboxes.innerHTML = ''
        categoryNotice.textContent = '请先选择月份以显示对应的薪酬类别'
        categoryNotice.style.display = 'block'
        return
      }

      const categoriesForMonth = monthCategoryMap[selectedMonth] || []

      if (categoriesForMonth.length === 0) {
        categoryCheckboxes.innerHTML = '<div style="color: #999; text-align: center; padding: 20px;">该月份没有薪酬类别数据</div>'
        categoryNotice.style.display = 'none'
        return
      }

      categoryNotice.style.display = 'none'
      categoryCheckboxes.innerHTML = categoriesForMonth.map(category => `
        <label style="
          display: block;
          margin-bottom: 8px;
          cursor: pointer;
          padding: 5px;
          border-radius: 3px;
          transition: background-color 0.2s;
        " onmouseover="this.style.backgroundColor='#e3f2fd'" onmouseout="this.style.backgroundColor='transparent'">
          <input type="checkbox" value="${category}" style="margin-right: 8px;">
          ${category}
        </label>
      `).join('')
    }

    // 月份选择变化事件
    monthSelect?.addEventListener('change', (e) => {
      const selectedMonth = (e.target as HTMLSelectElement).value
      updateCategoryOptions(selectedMonth)
    })

    // 确认按钮事件
    confirmBtn?.addEventListener('click', () => {
      const selectedMonth = monthSelect.value
      const checkboxes = document.querySelectorAll('#category-checkboxes input[type="checkbox"]:checked')
      const selectedCategories = Array.from(checkboxes).map(cb => (cb as HTMLInputElement).value)

      if (!selectedMonth) {
        alert('请选择月份！')
        return
      }

      if (selectedCategories.length === 0) {
        alert('请至少选择一个薪酬类别！')
        return
      }

      dialog?.remove()
      resolve({
        months: [selectedMonth],
        categories: selectedCategories
      })
    })

    // 取消按钮事件
    cancelBtn?.addEventListener('click', () => {
      dialog?.remove()
      resolve(null)
    })

    // 点击背景关闭
    dialog?.addEventListener('click', (e) => {
      if (e.target === dialog) {
        dialog.remove()
        resolve(null)
      }
    })
  })
}

// 核心功能方法（从原始文件迁移）
const matchIdAndProject = async () => {
  try {
    isLoading.value = true;

    // 获取各个表格的数据
    const taxCalculationData = getSheetDataByName('算税底稿');
    const idCardData = getSheetDataByName('身份证号表');
    const projectMappingData = getSheetDataByName('项目匹配表');
    const remoteTaxData = getSheetDataByName('异地纳税');
    const subsidyPackageData = getSheetDataByName('包干费');

    if (!taxCalculationData || taxCalculationData.length === 0) {
      alert('请先加载算税底稿数据！');
      return;
    }

    // 获取表头索引
    const taxHeader = taxCalculationData[0];
    const idCardHeader = idCardData?.[0] || [];
    const projectHeader = projectMappingData?.[0] || [];
    const remoteTaxHeader = remoteTaxData?.[0] || [];

    // 算税底稿表头索引
    const taxIdIdx = taxHeader.indexOf('身份证号');
    const taxNameIdx = taxHeader.indexOf('姓名');
    const taxProjectIdx = taxHeader.indexOf('成本所属项目');
    const taxFinanceProjectIdx = taxHeader.indexOf('财务标准项目');
    const taxFinanceUnitIdx = taxHeader.indexOf('财务标准单位');
    const taxMonthIdx = taxHeader.indexOf('月份');
    const taxRegionIdx = taxHeader.indexOf('算税地区');

    // 身份证号表头索引
    const idCardNameIdx = idCardHeader.indexOf('姓名');
    const idCardIdIdx = idCardHeader.indexOf('身份证号');

    // 项目匹配表头索引
    const projectHrIdx = projectHeader.indexOf('人资项目名称');
    const projectStdIdx = projectHeader.indexOf('标准项目名称');
    const projectCodeIdx = projectHeader.indexOf('标准项目编码');
    const projectUnitIdx = projectHeader.indexOf('财务标准单位');
    const projectUnitCodeIdx = projectHeader.indexOf('财务标准单位编码');

    // 异地纳税表头索引
    const remoteTaxIdIdx = remoteTaxHeader.indexOf('身份证号');
    const remoteTaxNameIdx = remoteTaxHeader.indexOf('姓名');
    const monthColumns = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];

    //包干费表头索引
    const subsidyPackageHeader = subsidyPackageData[0];
    const subsidyPackageIdIdx = subsidyPackageHeader.indexOf('身份证号');
    const subsidyPackageNameIdx = subsidyPackageHeader.indexOf('姓名');
    const subsidyPackageProjectIdx = subsidyPackageHeader.indexOf('成本所属项目');
    const subsidyPackageFinanceProjectIdx = subsidyPackageHeader.indexOf('财务标准项目');

    // 检查必要字段是否存在
    if (taxIdIdx === -1 || taxNameIdx === -1 || taxProjectIdx === -1 || taxMonthIdx === -1 || taxRegionIdx === -1) {
      alert('算税底稿缺少必要字段：身份证号、姓名、成本所属项目、月份、算税地区');
      return;
    }
    //对身份号表的名字建立字典
    let idCardDict = {};
    for (let i = 0; i < idCardData.length; i++) {
      //设置重名警告，在value中体现
      if (idCardData[i][idCardNameIdx] in idCardDict) {
        idCardData[i][idCardNameIdx] = idCardData[i][idCardNameIdx] + '（重名）';
      } else {
        idCardDict[idCardData[i][idCardNameIdx]] = idCardData[i][idCardIdIdx];
      }
    }
    //对异地纳税身份证号建立索引字典
    let remoteDict = {};
    for (let i = 0; i < remoteTaxData.length; i++) {
      remoteDict[remoteTaxData[i][remoteTaxIdIdx]] = i;
    }
    //对项目名称建立字典
    let projectDict = {};
    for (let i = 0; i < projectMappingData.length; i++) {
      projectDict[projectMappingData[i][projectHrIdx]] = i;
    }
    let updateCount = 0;
    let idCardArr = [];
    let remoteArr = [];
    let projectArr = [];
    for (let i = 1; i < taxCalculationData.length; i++) {

      //先匹配身份证号,注意跳过存在值
      if (taxCalculationData[i][taxIdIdx] == null || taxCalculationData[i][taxIdIdx] == '') {
        taxCalculationData[i][taxIdIdx] = idCardDict[taxCalculationData[i][taxNameIdx]]
        idCardArr.push([taxCalculationData[i][taxIdIdx]]);
      }
      else {
        idCardArr.push([taxCalculationData[i][taxIdIdx]]);
      }
      //匹配项目名称，第一个为财务标准项目，第二个财务标准单位，以人资项目名称为索引匹配
      //建立一个一维数组，然后peojectArr添加这个数组
      if (taxCalculationData[i][taxProjectIdx] in projectDict) {
        let ia = projectDict[taxCalculationData[i][taxProjectIdx]];
        projectArr.push([projectMappingData[ia][projectStdIdx], projectMappingData[ia][projectUnitIdx]]);
      } else { projectArr.push(["未找到项目", '']) }

      //匹配异地纳税
      let id = taxCalculationData[i][taxIdIdx]
      let ic = remoteDict[id];
      let ir = taxCalculationData[i][taxMonthIdx] + 2

      if (id in remoteDict) {
        let remotemap = remoteTaxData[ic][ir];
        if (remotemap !== '' && remotemap !== null) {
          remoteArr.push([remotemap])
        } else { remoteArr.push(["注册所在地"]) }
      } else { remoteArr.push(["注册所在地"]) }
      updateCount = updateCount + 1;
    }
    //回写数据
    const workbook = univerAPIInstance?.getActiveWorkbook()
    const sheets = workbook?.getSheets()
    const sheet = sheets.find(sheet => sheet.getSheet().getName() === '算税底稿')
    sheet.getRange(1, taxIdIdx, idCardArr.length, 1).setValues(idCardArr)
    sheet.getRange(1, taxRegionIdx, remoteArr.length, 1).setValues(remoteArr)
    sheet.getRange(1, taxFinanceProjectIdx, projectArr.length, 2).setValues(projectArr)

    //匹配包干费的项目和身份证号
    let subsidyPackageIDArr = [];
    let subsidyPackageProjectArr = [];
    for (let i = 1; i < subsidyPackageData.length; i++) {
      if ((subsidyPackageData[i][subsidyPackageIdIdx] == null || subsidyPackageData[i][subsidyPackageIdIdx] == '') && (subsidyPackageData[i][subsidyPackageNameIdx] in idCardDict)) {
        subsidyPackageIDArr.push([idCardDict[subsidyPackageData[i][subsidyPackageNameIdx]]]);
      }
      else {
        subsidyPackageIDArr.push([''])};
      if ( (subsidyPackageData[i][subsidyPackageFinanceProjectIdx] == null || subsidyPackageData[i][subsidyPackageFinanceProjectIdx] == '')  &&subsidyPackageData[i][subsidyPackageProjectIdx] in projectDict) {
        subsidyPackageProjectArr.push([projectDict[subsidyPackageData[i][subsidyPackageProjectIdx]]]);
      } else { subsidyPackageProjectArr.push(["未找到项目"]) }
      }
    //回写包干费的项目和身份证号
    const subsidyPackageSheet = sheets.find(sheet => sheet.getSheet().getName() === '包干费')
    subsidyPackageSheet.getRange(1, subsidyPackageIdIdx, subsidyPackageIDArr.length, 1).setValues(subsidyPackageIDArr)
    subsidyPackageSheet.getRange(1, subsidyPackageFinanceProjectIdx, subsidyPackageProjectArr.length, 1).setValues(subsidyPackageProjectArr)

    alert(`匹配完成！`);
  } catch (error) {
    console.error('匹配失败:', error);
    alert('匹配失败: ' + error.message);
  } finally {
    isLoading.value = false;
  }
}

const calculateTax = async () => {
  try {
    isLoading.value = true

    // ====== 新增：从univer的worksheet获取数据 ======
    if (!univerAPIInstance) {
      alert('Univer尚未初始化！')
      return
    }
    const workbook = univerAPIInstance.getActiveWorkbook()
    if (!workbook) {
      alert('未找到工作簿！')
      return
    }
    // 获取所有sheet
    const sheets = workbook.getSheets()
    // 找到"算税底稿"sheet
    const taxSheet = sheets.find(sheet => sheet.getSheet().getName() === '算税底稿')
    if (!taxSheet) {
      alert('未找到"算税底稿"工作表！')
      return
    }
    // 获取"算税底稿"数据
    const maxRow = taxSheet.getMaxRows()
    const maxCol = taxSheet.getMaxColumns()
    const currentData = maxRow > 0 && maxCol > 0 ? taxSheet.getRange(0, 0, maxRow, maxCol).getValues() : []

    // 找到"累计专项附加扣除表"sheet
    const specialSheet = sheets.find(sheet => sheet.getSheet().getName() === '累计专项附加扣除表')
    let specialDeductionData: any[][] = []
    if (specialSheet) {
      const sMaxRow = specialSheet.getMaxRows()
      const sMaxCol = specialSheet.getMaxColumns()
      specialDeductionData = sMaxRow > 0 && sMaxCol > 0 ? specialSheet.getRange(0, 0, sMaxRow, sMaxCol).getValues() : []
    }
    // ====== 新增结束 ======

    if (!currentData || currentData.length === 0) {
      alert('请先加载算税底稿数据！')
      return
    }

    const result = await taxCalculatorTs(currentData, specialDeductionData)

    // 更新数据到算税底稿工作表
    taxSheet.getRange(0, 0, result.length, result[0].length).setValues(result as any)

    alert('个税计算完成！')
  } catch (error) {
    console.error('计算个税失败:', error)
    alert('计算个税失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}




// 选择单个月份的弹窗
const selectMonth = (months: string[]): Promise<string | null> => {
  return new Promise((resolve) => {
    const monthOptions = months.map(m => `<option value="${m}">${m}</option>`).join('')
    const dialog = document.createElement('div')
    dialog.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; z-index: 9999;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;`
    dialog.innerHTML = `
      <div style="
        background: white; padding: 32px; border-radius: 12px; min-width: 320px; max-width: 400px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        animation: fadeInScale 0.2s ease-out;
      ">
        <style>
          @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
          }
        </style>
        <h3 style="margin: 0 0 20px 0; color: #1f2937; font-size: 18px; font-weight: 600;">
          选择申报月份
        </h3>
        <select id="monthSelect" style="
          width: 100%; padding: 12px; margin: 0 0 24px 0; border: 2px solid #e5e7eb;
          border-radius: 8px; font-size: 14px; background: white; outline: none;
          transition: border-color 0.2s;
        " onmouseover="this.style.borderColor='#3b82f6'" onmouseout="this.style.borderColor='#e5e7eb'">
          ${monthOptions}
        </select>
        <div style="display: flex; gap: 12px; justify-content: flex-end;">
          <button id="cancelBtn" style="
            padding: 10px 20px; border: 2px solid #e5e7eb; background: white; color: #6b7280;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s;
          " onmouseover="this.style.backgroundColor='#f9fafb'; this.style.borderColor='#d1d5db';"
             onmouseout="this.style.backgroundColor='white'; this.style.borderColor='#e5e7eb';">
            取消
          </button>
          <button id="okBtn" style="
            padding: 10px 20px; border: none; background: #3b82f6; color: white;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: background-color 0.2s;
          " onmouseover="this.style.backgroundColor='#2563eb';"
             onmouseout="this.style.backgroundColor='#3b82f6';">
            确定
          </button>
        </div>
      </div>`
    document.body.appendChild(dialog)

    const cleanup = () => {
      if (document.body.contains(dialog)) {
        document.body.removeChild(dialog)
      }
    }

      ; (dialog.querySelector('#cancelBtn') as HTMLButtonElement).onclick = () => { cleanup(); resolve(null) }
      ; (dialog.querySelector('#okBtn') as HTMLButtonElement).onclick = () => {
        const val = (dialog.querySelector('#monthSelect') as HTMLSelectElement).value
        cleanup()
        resolve(val)
      }

    // ESC键关闭
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        cleanup()
        resolve(null)
        document.removeEventListener('keydown', handleKeydown)
      }
    }
    document.addEventListener('keydown', handleKeydown)
  })
}

// 选择多个月份的弹窗
const selectMonth2 = (months: string[]): Promise<string[] | null> => {
  return new Promise((resolve) => {
    const selectedMonths = new Set<string>()

    const monthCheckboxes = months.map(month => `
      <label style="
        display: flex; align-items: center; padding: 10px 12px; 
        border-radius: 6px; cursor: pointer; transition: background-color 0.2s;
        margin-bottom: 4px;
      " onmouseover="this.style.backgroundColor='#f3f4f6'" onmouseout="this.style.backgroundColor='transparent'">
        <input type="checkbox" value="${month}" style="
          width: 16px; height: 16px; margin-right: 12px; cursor: pointer;
        ">
        <span style="font-size: 14px; color: #1f2937;">${month}</span>
      </label>
    `).join('')

    const dialog = document.createElement('div')
    dialog.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; z-index: 9999;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;`

    dialog.innerHTML = `
      <div style="
        background: white; padding: 24px; border-radius: 12px; min-width: 300px; max-width: 400px;
        max-height: 80vh; overflow-y: auto; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        animation: fadeInScale 0.2s ease-out;
      ">
        <style>
          @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
          }
          .select-all {
            padding: 8px 12px; margin-bottom: 12px;
            border-bottom: 1px solid #e5e7eb;
          }
          .month-list {
            max-height: 300px; overflow-y: auto;
            margin: 12px 0;
          }
          .selected-count {
            font-size: 13px; color: #6b7280; margin-top: 8px;
          }
        </style>
        
        <h3 style="margin: 0 0 16px 0; color: #1f2937; font-size: 18px; font-weight: 600;">
          选择申报月份（可多选）
        </h3>
        
        <div class="select-all" style="display: flex; align-items: center;">
          <input type="checkbox" id="selectAll" style="
            width: 16px; height: 16px; margin-right: 12px; cursor: pointer;
          ">
          <span style="font-size: 14px; color: #1f2937; font-weight: 500;">全选</span>
        </div>
        
        <div class="month-list">
          ${monthCheckboxes}
        </div>
        
        <div class="selected-count" id="selectedCount">
          已选择 0 个月份
        </div>
        
        <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 16px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
          <button id="cancelBtn" style="
            padding: 10px 20px; border: 2px solid #e5e7eb; background: white; color: #6b7280;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s; min-width: 80px;
          " onmouseover="this.style.backgroundColor='#f9fafb'; this.style.borderColor='#d1d5db';"
             onmouseout="this.style.backgroundColor='white'; this.style.borderColor='#e5e7eb';">
            取消
          </button>
          <button id="okBtn" style="
            padding: 10px 20px; border: none; background: #3b82f6; color: white;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: background-color 0.2s; min-width: 80px;
          " onmouseover="this.style.backgroundColor='#2563eb';"
             onmouseout="this.style.backgroundColor='#3b82f6';"
             disabled>
            确定
          </button>
        </div>
      </div>`

    document.body.appendChild(dialog)

    const updateSelectedCount = () => {
      const checkboxes = dialog.querySelectorAll<HTMLInputElement>('input[type="checkbox"]:not(#selectAll)')
      const selectedCheckboxes = Array.from(checkboxes).filter(cb => cb.checked)
      const selectedCount = selectedCheckboxes.length
      const okBtn = dialog.querySelector('#okBtn') as HTMLButtonElement
      const selectedCountEl = dialog.querySelector('#selectedCount') as HTMLDivElement

      selectedCountEl.textContent = `已选择 ${selectedCount} 个月份`
      okBtn.disabled = selectedCount === 0
      okBtn.style.opacity = selectedCount === 0 ? '0.6' : '1'
      okBtn.style.cursor = selectedCount === 0 ? 'not-allowed' : 'pointer'
    }

    // 全选/取消全选
    const selectAllCheckbox = dialog.querySelector('#selectAll') as HTMLInputElement
    selectAllCheckbox.addEventListener('change', (e) => {
      const checkboxes = dialog.querySelectorAll<HTMLInputElement>('input[type="checkbox"]:not(#selectAll)')
      checkboxes.forEach(checkbox => {
        checkbox.checked = (e.target as HTMLInputElement).checked
      })
      updateSelectedCount()
    })

    // 单个复选框变化时更新全选状态
    const monthCheckboxElements = dialog.querySelectorAll<HTMLInputElement>('input[type="checkbox"]:not(#selectAll)')
    monthCheckboxElements.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const allChecked = Array.from(monthCheckboxElements).every(cb => cb.checked)
        selectAllCheckbox.checked = allChecked
        updateSelectedCount()
      })
    })

    const cleanup = () => {
      if (document.body.contains(dialog)) {
        document.body.removeChild(dialog)
      }
    }

      ; (dialog.querySelector('#cancelBtn') as HTMLButtonElement).onclick = () => {
        cleanup()
        resolve(null)
      }

      ; (dialog.querySelector('#okBtn') as HTMLButtonElement).onclick = () => {
        const selected = Array.from(dialog.querySelectorAll<HTMLInputElement>('input[type="checkbox"]:checked:not(#selectAll)'))
          .map(cb => cb.value)
        cleanup()
        resolve(selected)
      }

    // ESC键关闭
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        cleanup()
        resolve(null)
        document.removeEventListener('keydown', handleKeydown)
      }
    }
    document.addEventListener('keydown', handleKeydown)

    // 初始更新计数
    updateSelectedCount()
  })
}

// 生成申报表数据
const generateDeclaration = async () => {
  const taxData = getSheetDataByName("算税底稿");
  const specialDeductionData = getSheetDataByName("累计专项附加扣除表");
  //过滤非空数据行
  const regionIdx = taxData[0].indexOf("算税地区");
  const monthIdx = taxData[0].indexOf("月份");
  const nameIdx = taxData[0].indexOf("姓名");
  const idCardIdx = taxData[0].indexOf("身份证号");
  const incomeIdx = taxData[0].indexOf("本期收入");
  const pensionAgeIdx = taxData[0].indexOf("基本养老保险费");
  const pensionMedicalIdx = taxData[0].indexOf("基本医疗保险费");
  const unemploymentInsuranceIdx = taxData[0].indexOf("失业保险费");
  const housingFundIdx = taxData[0].indexOf("住房公积金");
  const enterpriseAnnuityIdx = taxData[0].indexOf("企业(职业)年金");
  const otherDeductionIdx = taxData[0].indexOf("其它扣款");
  const ALLspecialDeductionIdx = taxData[0].indexOf("累计专项附加扣除");
  const taxAmountIdx = taxData[0].indexOf("本次税款");

  const spMonthIdx = specialDeductionData[0].indexOf("所得期间起");
  const spIdCardIdx = specialDeductionData[0].indexOf("证件号码");
  const spChildrenEducationIdx = specialDeductionData[0].indexOf("累计子女教育");
  const spContinuingEducationIdx = specialDeductionData[0].indexOf("累计继续教育");
  const spHousingLoanInterestIdx = specialDeductionData[0].indexOf("累计住房贷款利息");
  const spHousingRentIdx = specialDeductionData[0].indexOf("累计住房租金");
  const spSeniorCareIdx = specialDeductionData[0].indexOf("累计赡养老人");
  const spChildCareIdx = specialDeductionData[0].indexOf("累计3岁以下婴幼儿照护");
  const spPersonalPensionIdx = specialDeductionData[0].indexOf("累计个人养老金");



  const monthList = taxData.map(item => item[monthIdx]).filter(item => item !== null && item !== "");
  const selectedMonth = await selectMonth(monthList)
  if (selectedMonth) {
    // 创建二重字典，用于按身份证号累加各项数据
    const idCardAccumulator = {}
    for (let i = 1; i < taxData.length; i++) {
      const row = taxData[i]
      if (row[monthIdx] != selectedMonth) {
        continue
      }
      const idCard = row[idCardIdx]
      const income = parseFloat(row[incomeIdx] || 0) - parseFloat(row[otherDeductionIdx] || 0)
      const pensionAge = parseFloat(row[pensionAgeIdx] || 0)
      const pensionMedical = parseFloat(row[pensionMedicalIdx] || 0)
      const unemploymentInsurance = parseFloat(row[unemploymentInsuranceIdx] || 0)
      const housingFund = parseFloat(row[housingFundIdx] || 0)
      const enterpriseAnnuity = parseFloat(row[enterpriseAnnuityIdx] || 0)
      const ALLspecialDeduction = parseFloat(row[ALLspecialDeductionIdx] || 0)
      const taxAmount = parseFloat(row[taxAmountIdx] || 0)

      // 初始化该身份证号的累加器（如果不存在）
      if (!idCardAccumulator[idCard]) {
        idCardAccumulator[idCard] = {
          income: 0,
          pensionAge: 0,
          pensionMedical: 0,
          unemploymentInsurance: 0,
          housingFund: 0,
          enterpriseAnnuity: 0,
          ALLspecialDeduction: 0,
          taxAmount: 0,
          specialDeduction: 0,
          count: 0  // 记录该身份证号出现的次数
        }
      }

      // 累加各项数据
      const acc = idCardAccumulator[idCard]
      acc.income += income
      acc.pensionAge += pensionAge
      acc.pensionMedical += pensionMedical
      acc.unemploymentInsurance += unemploymentInsurance
      acc.housingFund += housingFund
      acc.enterpriseAnnuity += enterpriseAnnuity
      acc.ALLspecialDeduction = ALLspecialDeduction //这个无需累加
      acc.taxAmount += taxAmount
      acc.region = row[regionIdx]
      acc.name = row[nameIdx]
      acc.count++
    }

    // 现在idCardAccumulator中包含了按身份证号累加的所有数据
    // 可以进一步处理或返回这个累加器

    // 拼接专项附加，
    for (let i = 1; i < specialDeductionData.length; i++) {
      let month = specialDeductionData[i][spMonthIdx]
      if (month == null || month == "") {
        continue
      }
      //提取月份，字符，两个xxxx-xx-xx或xxxx/xx/xx,通过-或/来判断，然后整数化
      month = parseInt(month.split("-")[1] || month.split("/")[1])
      if (month != selectedMonth) {
        continue
      }
      //提取身份证号
      let idCard = specialDeductionData[i][spIdCardIdx]
      //如果身份证号存在idcardmap
      if (idCard in idCardAccumulator) {
        let acc = idCardAccumulator[idCard]
        acc.spChildrenEducation = parseFloat(specialDeductionData[i][spChildrenEducationIdx] || 0)
        acc.spContinuingEducation = parseFloat(specialDeductionData[i][spContinuingEducationIdx] || 0)
        acc.spHousingLoanInterest = parseFloat(specialDeductionData[i][spHousingLoanInterestIdx] || 0)
        acc.spHousingRent = parseFloat(specialDeductionData[i][spHousingRentIdx] || 0)
        acc.spSeniorCare = parseFloat(specialDeductionData[i][spSeniorCareIdx] || 0)
        acc.spChildCare = parseFloat(specialDeductionData[i][spChildCareIdx] || 0)
        acc.spPersonalPension = parseFloat(specialDeductionData[i][spPersonalPensionIdx] || 0)
        if (Math.abs(acc.ALLspecialDeduction - acc.spChildrenEducation - acc.spContinuingEducation - acc.spHousingLoanInterest - acc.spHousingRent - acc.spSeniorCare - acc.spChildCare - acc.spPersonalPension) > 0) {
          acc.remark = "累计专项附加扣除与实际不符"
        }
      }


    }

    const declarationForm = []
    declarationForm.push([
      '工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
      '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
      '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
      '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
      '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
      '准予扣除的捐赠额', '减免税额', '备注'
    ])
    //遍历字典，将其数据回写数组
    for (let idCard in idCardAccumulator) {
      let acc = idCardAccumulator[idCard]
      declarationForm.push([
        acc.region,
        acc.name,
        '身份证',
        acc.idCard,
        acc.income,
        0,
        acc.pensionAge,
        acc.pensionMedical,
        acc.unemploymentInsurance,
        acc.housingFund,
        acc.spChildrenEducation || 0,
        acc.spContinuingEducation || 0,
        acc.spHousingLoanInterest || 0,
        acc.spHousingRent || 0,
        acc.spSeniorCare || 0,
        acc.spChildCare || 0,
        acc.spPersonalPension,
        acc.enterpriseAnnuity,
        0,
        0,
        0,
        0,
        0,
        acc.remark || acc.taxAmount])
    }
    //回写数据到univer的个税申报表
    //先处理数字格式
    processIdColumnsAsText(declarationForm)
    const workbook = univerAPIInstance.getActiveWorkbook();
    const sheets = workbook?.getSheets();
    const sheet = sheets?.find(s => s.getSheet().getName() === '个税申报表');
    sheet.deleteRows(0, sheet.getSheet().getRowCount())
    sheet.insertRows(0, declarationForm.length + 1)
    sheet.getRange(0, 0, declarationForm.length, declarationForm[0].length).setValues(declarationForm)
    alert('个税申报表已生成')
  }
}



const generateDivision = async () => {
  // TODO: 实现生成劳务派遣分割表功能
  console.log('生成劳务派遣分割表')
}

const pushDeclaration = async () => {
  try {
    isLoading.value = true

    // 获取算税底稿数据
    const taxCalculationData = getSheetDataByName('算税底稿');
    const idCardData = getSheetDataByName('身份证号表');
    const projectMappingData = getSheetDataByName('项目匹配表');

    if (!taxCalculationData || taxCalculationData.length === 0) {
      alert('请先加载算税底稿数据！')
      return
    }

    // 提取月份和薪酬类别
    const { months, salaryCategories, monthCategoryMap } = extractMonthsAndCategories(taxCalculationData)

    if (months.length === 0) {
      alert('算税底稿中没有找到月份数据！')
      return
    }

    if (salaryCategories.length === 0) {
      alert('算税底稿中没有找到薪酬类别数据！')
      return
    }

    // 显示选择对话框
    const selectedOptions = await showDeclarationDialog(months, salaryCategories, monthCategoryMap)

    if (!selectedOptions) {
      return // 用户取消了操作
    }

    // 发送数据到后台处理
    const response = await fetch('http://localhost:8000/api/push-declaration', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taxCalculationData: taxCalculationData,
        selectedMonths: selectedOptions.months,
        selectedCategories: selectedOptions.categories,
        idCardData: idCardData,
        projectMappingData: projectMappingData,
        year: selectedYear.value
      })
    })

    if (!response.ok) {
      throw new Error(`推送申报失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      alert(`推送申报成功！${result.message || ''}`)
    } else {
      alert('推送申报失败: ' + (result.message || '未知错误'))
    }

  } catch (error) {
    console.error('推送申报失败:', error)
    alert('推送申报失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

const buildFinanceTemplate = async () => {
  const adjustmentData = getSheetDataByName("薪酬总表(无一次性年终)");
  const projectMappingData = getSheetDataByName("项目匹配表");
  const monthIdx = adjustmentData[0].indexOf("月份");
  const monthLIst = adjustmentData.slice(1).map(item => item[monthIdx]).filter(item => item !== null && item != "");
  const selectedMonth = await selectMonth2(monthLIst)
  if (!adjustmentData || adjustmentData.length === 0) {
    alert('请先加载薪酬总表数据！')
    return
  }
  if (!selectedMonth || selectedMonth.length === 0) {
    alert('请先选择申报月份！')
    return
  }
  //将薪酬总表,项目匹配表的数据和选择的月份都发送给后台，让后台处理
  const response = await fetch('http://localhost:8000/api/build-finance-template', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      adjustmentData: adjustmentData,
      projectMappingData: projectMappingData,
      selectedMonth: selectedMonth,
      year: selectedYear.value
    })
  })

  if (!response.ok) {
    throw new Error(`生成财务模板失败: ${response.status} ${response.statusText}`)
  }

  const result = await response.json()

  if (result.code === 200) {
    alert(`生成财务模板成功！${result.message || ''}`)
  } else {
    alert('生成财务模板失败: ' + (result.message || '未知错误'))
  }
}

const exportAllTablesToExcel = async () => {
  try {
    isLoading.value = true

    const workbook = new ExcelJS.Workbook()

    // 为每个标签页创建工作表并导出数据
    for (let i = 0; i < tabs.value.length; i++) {
      const tab = tabs.value[i]
      const data = tabData.value[tab.key] || []

      if (data.length > 0) {
        const worksheet = workbook.addWorksheet(tab.name)

        // 添加数据
        data.forEach((row: any[], index: number) => {
          worksheet.addRow(row)

          // 设置表头样式
          if (index === 0) {
            const headerRow = worksheet.getRow(1)
            headerRow.font = { bold: true }
            headerRow.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFF0F0F0' }
            }
          }
        })

        // 自动调整列宽
        worksheet.columns.forEach(column => {
          column.width = 15
        })
      }
    }

    // 如果没有数据，至少创建一个空工作表
    if (workbook.worksheets.length === 0) {
      workbook.addWorksheet('空数据')
    }

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `薪酬个税数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    alert('导出成功！')
  } catch (error) {
    console.error('导出所有表格失败:', error)
    alert('导出所有表格失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 触发专项附加扣除导入
const triggerSpecialDeductionImport = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

// 处理专项附加扣除导入
const handleSpecialDeductionImport = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    try {
      // 选择工作表
      const selectedWorksheet = await selectWorksheetFromExcel(file)
      if (!selectedWorksheet) {
        target.value = ''
        return
      }

      // 弹出选择导入模式的对话框
      const importMode = await selectImportMode()
      if (importMode) {
        await importSpecialDeductionFromExcel(file, importMode, selectedWorksheet)
      }
    } catch (error) {
      console.error('专项附加扣除导入失败:', error)
      alert('专项附加扣除导入失败: ' + error.message)
    } finally {
      // 清空文件输入，允许重复选择同一文件
      target.value = ''
    }
  }
}

// 选择导入模式的弹窗
const selectImportMode = (): Promise<'overwrite' | 'append' | null> => {
  return new Promise((resolve) => {
    const dialog = document.createElement('div')
    dialog.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; z-index: 9999;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;`
    dialog.innerHTML = `
      <div style="
        background: white; padding: 32px; border-radius: 12px; min-width: 320px; max-width: 400px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        animation: fadeInScale 0.2s ease-out;
      ">
        <style>
          @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
          }
        </style>
        <h3 style="margin: 0 0 20px 0; color: #1f2937; font-size: 18px; font-weight: 600;">
          选择导入模式
        </h3>
        <p style="margin: 0 0 24px 0; color: #6b7280; font-size: 14px; line-height: 1.5;">
          请选择专项附加扣除数据的导入方式：
        </p>
        <div style="display: flex; flex-direction: column; gap: 12px; margin-bottom: 24px;">
          <button id="overwriteBtn" style="
            padding: 12px 16px; border: 2px solid #3b82f6; background: #3b82f6; color: white;
            border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s; text-align: left;
          " onmouseover="this.style.backgroundColor='#2563eb'; this.style.borderColor='#2563eb';"
             onmouseout="this.style.backgroundColor='#3b82f6'; this.style.borderColor='#3b82f6';">
            <strong>覆盖导入</strong><br>
            <small style="opacity: 0.8;">清空现有数据，使用新数据替换</small>
          </button>
          <button id="appendBtn" style="
            padding: 12px 16px; border: 2px solid #10b981; background: #10b981; color: white;
            border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s; text-align: left;
          " onmouseover="this.style.backgroundColor='#059669'; this.style.borderColor='#059669';"
             onmouseout="this.style.backgroundColor='#10b981'; this.style.borderColor='#10b981';">
            <strong>追加导入</strong><br>
            <small style="opacity: 0.8;">保留现有数据，在末尾添加新数据</small>
          </button>
        </div>
        <div style="display: flex; gap: 12px; justify-content: flex-end;">
          <button id="cancelBtn" style="
            padding: 10px 20px; border: 2px solid #e5e7eb; background: white; color: #6b7280;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s;
          " onmouseover="this.style.backgroundColor='#f9fafb'; this.style.borderColor='#d1d5db';"
             onmouseout="this.style.backgroundColor='white'; this.style.borderColor='#e5e7eb';">
            取消
          </button>
        </div>
      </div>`
    document.body.appendChild(dialog)

    const cleanup = () => {
      if (document.body.contains(dialog)) {
        document.body.removeChild(dialog)
      }
    }

      ; (dialog.querySelector('#cancelBtn') as HTMLButtonElement).onclick = () => { cleanup(); resolve(null) }
      ; (dialog.querySelector('#overwriteBtn') as HTMLButtonElement).onclick = () => { cleanup(); resolve('overwrite') }
      ; (dialog.querySelector('#appendBtn') as HTMLButtonElement).onclick = () => { cleanup(); resolve('append') }

    // ESC键关闭
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        cleanup()
        resolve(null)
        document.removeEventListener('keydown', handleKeydown)
      }
    }
    document.addEventListener('keydown', handleKeydown)
  })
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  await initUniver()

  // 不需要再次加载数据，initUniver已经加载了所有初始数据到对应的工作表
  // setTimeout(() => {
  //   loadDataToCurrentSheet(tabData.value.taxDeclaration)
  // }, 1000)
})

// 组件卸载
onBeforeUnmount(() => {
  if (univerInstance) {
    univerInstance.dispose()
  }
})

// 获取当前工作表行数
const getCurrentSheetRowCount = (): number => {
  const data = getCurrentSheetData()
  return Math.max(0, data.length - 1) // 减去表头行
}

// 刷新当前工作表
const refreshCurrentSheet = () => {
  const tabKey = tabs.value[activeTabIndex.value]?.key
  if (tabKey && tabData.value[tabKey]) {
    loadDataToCurrentSheet(tabData.value[tabKey])
    lastUpdateTime.value = new Date().toLocaleTimeString()
  }
}

// 按年获取后台数据
const fetchAllData = async () => {
  if (!selectedYear.value) {
    alert('请先选择年份！')
    return
  }
  try {
    const response = await fetch('http://localhost:8000/api/get-salay-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ year: selectedYear.value })
    });

    if (!response.ok) {
      throw new Error(`获取快照失败: ${response.status} ${response.statusText}`);
    }

    const snapsheet = await response.json();
    const unitId = univerAPIInstance?.getActiveWorkbook()?.getId();
    if (unitId) {
      univerAPIInstance?.disposeUnit(unitId);
    }
    univerAPIInstance?.createWorkbook(snapsheet);
  } catch (error) {
    console.error('导入数据时出错:', error);
    alert('导入数据失败: ' + (error.message || '未知错误'));
  }
}

const saveAllData = async () => {
  if (!selectedYear.value) {
    alert('请先选择年份！')
    return
  }
  const snapsheet = univerAPIInstance?.getActiveWorkbook()?.save();
  try {
    console.log('开始保存快照...');
    const response = await fetch('http://localhost:8000/api/save-salay-snapshot', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        year: selectedYear.value,
        snapshot: snapsheet
      }),
      cache: 'no-cache'
    });

    console.log('请求发送完成，状态:', response.status, response.ok ? '成功' : '失败');

    if (!response.ok) {
      throw new Error(`保存快照失败: ${response.status} ${response.statusText}`);
    }

    alert('快照已成功保存！');
  } catch (error) {
    console.error('保存快照出错:', error);
    alert('保存快照失败: ' + error.message);
  }
}


// 初始化加载默认数据
const loadInitialData = () => {
  const defaultData = initializeDefaultData()
  tabData.value = defaultData

  // 加载默认数据到当前标签页
  if (activeTabIndex.value === 0) {
    loadDataToCurrentSheet(defaultData.taxDeclaration)
  }
}

// 触发收入导入
const triggerIncomeImport = () => {
  if (incomeFileInput.value) {
    incomeFileInput.value.click()
  }
}

// 处理收入导入
const handleIncomeImport = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    try {
      isLoading.value = true

      // 选择工作表
      const selectedWorksheet = await selectWorksheetFromExcel(file)
      if (!selectedWorksheet) {
        target.value = ''
        return
      }

      // 导入收入数据
      await importIncomeFromExcel(file, selectedWorksheet)
    } catch (error) {
      console.error('收入导入失败:', error)
      alert('收入导入失败: ' + error.message)
    } finally {
      isLoading.value = false
      target.value = ''
    }
  }
}

// 选择Excel工作表
const selectWorksheetFromExcel = async (file: File): Promise<string | null> => {
  try {
    const workbook = new ExcelJS.Workbook()
    const arrayBuffer = await file.arrayBuffer()
    await workbook.xlsx.load(arrayBuffer)

    const worksheetNames = workbook.worksheets.map(ws => ws.name)

    if (worksheetNames.length === 1) {
      return worksheetNames[0]
    }

    return await showWorksheetSelectionDialog(worksheetNames)
  } catch (error) {
    console.error('读取Excel工作表失败:', error)
    throw new Error('无法读取Excel文件的工作表')
  }
}

// 显示工作表选择对话框
const showWorksheetSelectionDialog = (worksheetNames: string[]): Promise<string | null> => {
  return new Promise((resolve) => {
    const dialog = document.createElement('div')
    dialog.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; z-index: 9999;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;`

    const worksheetOptions = worksheetNames.map(name =>
      `<option value="${name}">${name}</option>`
    ).join('')

    dialog.innerHTML = `
      <div style="
        background: white; padding: 32px; border-radius: 12px; min-width: 320px; max-width: 400px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        animation: fadeInScale 0.2s ease-out;
      ">
        <style>
          @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
          }
        </style>
        <h3 style="margin: 0 0 20px 0; color: #1f2937; font-size: 18px; font-weight: 600;">
          选择工作表
        </h3>
        <p style="margin: 0 0 16px 0; color: #6b7280; font-size: 14px;">
          Excel文件包含多个工作表，请选择要导入的工作表：
        </p>
        <select id="worksheetSelect" style="
          width: 100%; padding: 12px; margin: 0 0 24px 0; border: 2px solid #e5e7eb;
          border-radius: 8px; font-size: 14px; background: white; outline: none;
          transition: border-color 0.2s;
        ">
          ${worksheetOptions}
        </select>
        <div style="display: flex; gap: 12px; justify-content: flex-end;">
          <button id="cancelBtn" style="
            padding: 10px 20px; border: 2px solid #e5e7eb; background: white; color: #6b7280;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s;
          ">
            取消
          </button>
          <button id="okBtn" style="
            padding: 10px 20px; border: none; background: #3b82f6; color: white;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: background-color 0.2s;
          ">
            确定
          </button>
        </div>
      </div>`

    document.body.appendChild(dialog)

    const cleanup = () => {
      if (document.body.contains(dialog)) {
        document.body.removeChild(dialog)
      }
    }

      ; (dialog.querySelector('#cancelBtn') as HTMLButtonElement).onclick = () => {
        cleanup()
        resolve(null)
      }

      ; (dialog.querySelector('#okBtn') as HTMLButtonElement).onclick = () => {
        const selectedWorksheet = (dialog.querySelector('#worksheetSelect') as HTMLSelectElement).value
        cleanup()
        resolve(selectedWorksheet)
      }

    // ESC键关闭
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        cleanup()
        resolve(null)
        document.removeEventListener('keydown', handleKeydown)
      }
    }
    document.addEventListener('keydown', handleKeydown)
  })
}

// 从Excel导入收入数据
const importIncomeFromExcel = async (file: File, worksheetName: string) => {
  try {
    const workbook = new ExcelJS.Workbook()
    const arrayBuffer = await file.arrayBuffer()
    await workbook.xlsx.load(arrayBuffer)

    const worksheet = workbook.getWorksheet(worksheetName)
    if (!worksheet) {
      throw new Error(`未找到工作表: ${worksheetName}`)
    }

    // 获取标题映射表
    const headerMappingData = getSheetDataByName('配置表') // 使用项目匹配表作为参考
    // 读取Excel数据
    // 构建标题映射字典
    const headerDict: Record<string, string> = {};
    if (Array.isArray(headerMappingData) && headerMappingData.length > 1) {
      headerMappingData.slice(1).forEach(row => {
        if (row[0] && row[1]) {
          headerDict[row[0].trim()] = row[1].trim();
        }
      });
    }
    let newData = []

    // 需要提取的字段及其在headerDict中的映射
    const requiredFields = ['姓名', '成本所属项目', '本期收入', '基本养老保险费', '住房公积金', '基本医疗保险费', '失业保险费', '企业(职业)年金', '其它扣款']
    const columnIndexMap: Record<string, number> = {}

    let startRow = 1
    // 遍历worksheet前五行，查找所需字段的列号
    for (let rowIndex = 0; rowIndex <= Math.min(5, worksheet.rowCount); rowIndex++) {
      const row = worksheet.getRow(rowIndex)

      row.eachCell((cell, colNumber) => {
        const cellValue = cell.value ? String(cell.value).trim() : ''

        // 检查是否匹配所需字段
        requiredFields.forEach(field => {
          // 直接匹配字段名
          if (cellValue === field) {
            columnIndexMap[field] = colNumber
            startRow = rowIndex
          }
          // 通过headerDict映射匹配
          else if (headerDict[cellValue] === field) {
            columnIndexMap[field] = colNumber
            startRow = rowIndex
          }
        })
      })

      // 如果找到了所有必需字段，可以提前结束搜索
      if (Object.keys(columnIndexMap).length >= requiredFields.length) {
        break
      }
    }
    console.log(columnIndexMap)

    // 遍历数据行，构造newData
    // 使用原生的行迭代方式，这样可以完全控制循环
    const rows = worksheet.getRows(0,worksheet.rowCount+1) || [];
    
    for (let rowIndex = startRow+1; rowIndex < rows.length+1; rowIndex++) {
      const row = rows[rowIndex];
      if (!row) continue;
      
      // 检查是否包含"合计"行，如果是则跳出
      let hasTotal = false;
      row.eachCell((cell) => {
        const cellValue = cell.value ? String(cell.value) : '';
        if (/.*合.*计.*/.test(cellValue)) {
          hasTotal = true;
        }
      });
      
      if (hasTotal) {
        // 完全终止整个循环
        break;
      }

      // 获取本期收入的值，检查是否为空或0
      const incomeColIndex = columnIndexMap['本期收入']
      if (incomeColIndex) {
        const incomeCell = row.getCell(incomeColIndex)
        const incomeValue = incomeCell.value

        // 如果本期收入为空或0，跳过此行
        if (!incomeValue || incomeValue === 0 || incomeValue === '0') {
          return
        }
      }

      // 构造数据行
      const dataRow: any[] = []
      requiredFields.forEach(field => {
        const colIndex = columnIndexMap[field]
        if (colIndex) {
          const cell = row.getCell(colIndex)
          dataRow.push(cell.value || '')
        } else {
          dataRow.push('') // 如果找不到对应列，填入空值
        }
      })
      //dataRow前五个值插入null，第四到五插入两个null
      const result = [
        ...Array(6).fill(null),
        ...dataRow.slice(0, 2),
        null, null,'工资',
        ...dataRow.slice(2)
      ];
      newData.push(result)
    }
    // 更新到算税底稿工作表
    const workbook_univer = univerAPIInstance?.getActiveWorkbook()
    const sheets = workbook_univer?.getSheets()
    const taxSheet = sheets?.find(sheet => sheet.getSheet().getName() === '算税底稿')

    if (!taxSheet) {
      throw new Error('未找到"算税底稿"工作表')
    }

    // 处理身份证号列为文本格式
    const processedData = processIdColumnsAsText(newData)

    // 更新工作表
    const requiredRows = Math.max(processedData.length, 1)
    const currentRows = taxSheet.getMaxRows()
    taxSheet.insertRows(taxSheet.getMaxRows(), requiredRows)
    taxSheet.getRange(currentRows, 0, processedData.length, processedData[0].length).setValues(processedData)
    alert(`成功导入收入数据 ${processedData.length} 行到算税底稿！`)
  } catch (error) {
    console.error('导入收入数据失败:', error)
    throw error
  }
}



function getSheetDataByName(sheetName: string): any[][] {
  if (!univerAPIInstance) return [];
  const workbook = univerAPIInstance.getActiveWorkbook();
  const sheets = workbook?.getSheets();
  const sheet = sheets?.find(s => s.getSheet().getName() === sheetName);
  if (!sheet) return [];
  const maxRow = sheet.getMaxRows();
  const maxCol = sheet.getMaxColumns();
  if (maxRow > 0 && maxCol > 0) {
    return sheet.getRange(0, 0, maxRow, maxCol).getValues();
  }
  return [];
}
</script>

<style scoped>
.salary-tax2-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 紧凑头部样式 */
.compact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px 30px;
  min-height: 60px;
}

.header-content {
  display: flex;
  align-items: center;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title-icon {
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 30px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button-icon {
  font-size: 16px;
}

/* 移除Tab相关样式，因为Univer自带Tab功能 */

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: white;
  font-size: 16px;
  margin-top: 16px;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.univer-container {
  flex: 1;
  background: white;
  margin: 0 20px 20px 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  min-height: 600px;
}

.univer-container.loading {
  pointer-events: none;
  opacity: 0.7;
}

.univer-spreadsheet {
  width: 100%;
  height: 100%;
  min-height: 600px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .compact-header {
    flex-direction: column;
    gap: 12px;
    padding: 15px 20px;
  }

  .header-controls {
    width: 100%;
    justify-content: flex-start;
  }

  .page-title {
    font-size: 18px;
  }

  .action-buttons {
    padding: 12px 20px;
  }

  .action-button {
    padding: 8px 10px;
    font-size: 11px;
  }

  .univer-container {
    margin: 0 15px 15px 15px;
  }

  .year-select,
  .fetch-button,
  .save-button {
    font-size: 12px;
    padding: 8px 12px;
  }
}

/* 年份选择和数据控制按钮样式 */
.year-select {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  backdrop-filter: blur(10px);
}

.year-select:focus {
  border-color: rgba(255, 255, 255, 0.4);
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.year-select option {
  background: #333;
  color: white;
}

.fetch-button,
.save-button {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(10px);
}

.fetch-button {
  background: rgba(16, 185, 129, 0.8);
}

.save-button {
  background: rgba(59, 130, 246, 0.8);
}

.fetch-button:hover:not(:disabled),
.save-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.fetch-button:disabled,
.save-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}
</style>
