<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon><ArrowLeft /></el-icon>
        返回主页
      </el-button>
      <h1 class="title">自动制证 - 智能凭证生成</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <div
          v-for="module in modules"
          :key="module.key"
          :class="['nav-tab', { active: activeModule === module.key }]"
          @click="setActiveModule(module.key)"
        >
          <el-icon><component :is="module.icon" /></el-icon>
          <span>{{ module.title }}</span>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const activeModule = ref('create')

const modules = [
  {
    key: 'create',
    title: '凭证生成',
    icon: 'Document',
    component: 'div' // 临时占位组件
  },
  {
    key: 'templates',
    title: '模板管理',
    icon: 'Files',
    component: 'div' // 临时占位组件
  },
  {
    key: 'batch',
    title: '批量处理',
    icon: 'Collection',
    component: 'div' // 临时占位组件
  }
]

const currentComponent = computed(() => {
  const module = modules.find(m => m.key === activeModule.value)
  return module ? module.component : 'div'
})

const setActiveModule = (key) => {
  activeModule.value = key
}

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.statistics-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  position: relative;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
  flex-shrink: 0;
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  overflow: hidden;
  background: #f5f7fa;
  height: 100%;
}

.content-area > * {
  height: 100%;
  overflow: auto;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.nav-tabs::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>