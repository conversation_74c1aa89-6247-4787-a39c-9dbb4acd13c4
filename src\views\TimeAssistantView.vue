<template>
  <div class="page-container">
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon><ArrowLeft /></el-icon>
        返回主页
      </el-button>
      <h1 class="title">时间助手</h1>
    </div>
    
    <div class="content">
      <el-empty description="功能开发中，敬请期待..." />
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.page-container {
  padding: 20px;
  height: 100vh;
  background: #f5f7fa;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.title {
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.content {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
}
</style>