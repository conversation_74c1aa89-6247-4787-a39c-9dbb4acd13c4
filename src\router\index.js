import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import StatisticsView from '../views/StatisticsView.vue'
import TimeAssistantView from '../views/TimeAssistantView.vue'
import FundManagerView from '../views/FundManagerView.vue'
import BudgetReportView from '../views/BudgetReportView.vue'
import SalaryTax2View from '../views/SalaryTax2View.vue'
import QuickQueryView from '../views/QuickQueryView.vue'
import QuickFillView from '../views/QuickFillView.vue'
import DataAnalysisView from '../views/DataAnalysisView.vue'
import CapitalFlowView from '../views/CapitalFlowView.vue'
import SamrtWorkerView from '../views/SamrtWorkerView.vue'
import SmartReconciliationView from '../views/SmartReconciliationView.vue'
import SettingsView from '../views/SettingsView.vue'
import ProjectReportView from '../views/ProjectReportView.vue'
import MyEditorView from '../views/MyEditorView.vue'
import ExcelDataView from '../views/ExcelDataView.vue'

// 导入新创建的功能模块组件
import StatisticsMaster from '../features/StatisticsMaster.vue'
import AutoVoucher from '../features/AutoVoucher.vue'
import FundManager from '../features/FundManager.vue'
import SmartAudit from '../features/SmartAudit.vue'
import OneClickClose from '../features/OneClickClose.vue'
import SmartTax from '../features/SmartTax.vue'
import QuickFill from '../features/QuickFill.vue'
import ReportAssistant from '../features/ReportAssistant.vue'
import ElectronicArchive from '../features/ElectronicArchive.vue'
import SystemSync from '../features/SystemSync.vue'
import Settings from '../features/Settings.vue'
import Others from '../features/Others.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/statistics',
      name: 'statistics',
      component: StatisticsMaster
    },
    {
      path: '/time-assistant',
      name: 'time-assistant',
      component: TimeAssistantView
    },
    {
      path: '/fund-manager',
      name: 'fund-manager',
      component: FundManager
    },
    {
      path: '/smart-check',
      name: 'smart-check',
      component: () => import('../views/TimeAssistantView.vue') // 临时使用占位页面
    },
    {
      path: '/voucher-assistant',
      name: 'voucher-assistant',
      component: () => import('../views/TimeAssistantView.vue') // 临时使用占位页面
    },
    {
      path: '/report-master',
      name: 'report-master',
      component: () => import('../views/TimeAssistantView.vue') // 临时使用占位页面
    },
    {
      path: '/report-assistant',
      name: 'report-assistant',
      component: ReportAssistant
    },
    {
      path: '/electronic-seal',
      name: 'electronic-seal',
      component: () => import('../views/TimeAssistantView.vue') // 临时使用占位页面
    },
    {
      path: '/system-visit',
      name: 'system-visit',
      component: () => import('../views/TimeAssistantView.vue') // 临时使用占位页面
    },
    {
      path: '/web-navigation',
      name: 'web-navigation',
      component: () => import('../views/TimeAssistantView.vue') // 临时使用占位页面
    },
    {
      path: '/auto-voucher',
      name: 'auto-voucher',
      component: AutoVoucher
    },
    {
      path: '/smart-audit',
      name: 'smart-audit',
      component: SmartAudit
    },
    {
      path: '/one-click-close',
      name: 'one-click-close',
      component: OneClickClose
    },
    {
      path: '/smart-tax',
      name: 'smart-tax',
      component: SmartTax
    },
    {
      path: '/electronic-archive',
      name: 'electronic-archive',
      component: ElectronicArchive
    },
    {
      path: '/system-sync',
      name: 'system-sync',
      component: SystemSync
    },
    {
      path: '/others',
      name: 'others',
      component: Others
    },
    {
      path: '/data-analysis',
      name: 'data-analysis',
      component: DataAnalysisView,
      meta: {
        title: '我的账面',
        icon: 'DataAnalysis'
      }
    },
    {
      path: '/budget-report',
      name: 'budget-report',
      component: BudgetReportView,
      meta: {
        title: '报表快算',
        icon: 'TrendCharts'
      }
    },
    // {
    //   path: '/salary-tax',
    //   name: 'salary-tax',
    //   component: SalaryTaxView,
    //   meta: {
    //     title: '薪酬个税',
    //     icon: 'Money'
    //   }
    // },
    {
      path: '/salary-tax',
      name: 'salary-tax',
      component: SalaryTax2View,
      meta: {
        title: '薪酬个税',
        icon: 'Money'
      }
    },
    {
      path: '/capital-flow',
      name: 'capital-flow',
      component: CapitalFlowView,
      meta: {
        title: '资金流动',
        icon: 'SwitchButton'
      }
    },
    //{
    //  path: '/one-click-report',
    //  name: 'one-click-report',
    //  component: OneClickReportView,
    //  meta: {
    //    title: '一键报告',
    //    icon: 'Document'
    //  }
    // },
    {
      path: '/project-report',
      name: 'project-report',
      component: ProjectReportView,
      meta: {
        title: '项目台账',
        icon: 'Folder'
      }
    },
    //{
    //   path: '/my-editor',
    //  name: 'my-editor',
    //component: MyEditorView,
    //meta: {
    //  title: '我的编辑器',
    //  icon: 'Edit'
    //}
    //},
  //{
   // path: '/excel-data',
   // name: 'excel-data',
   // component: ExcelDataView,
   // meta: {
   //   title: 'Excel数据表',
   //   icon: 'Grid'
   // }
  //},
  {
    path: '/samrt-worker',
    name: 'samrt-worker',
    component: SamrtWorkerView,
    meta: {
      title: '工人专区',
      icon: 'User'
    }
  },
  {
    path: '/smart-reconciliation',
    name: 'smart-reconciliation',
    component: SmartReconciliationView,
    meta: {
      title: '对账抵消',
      icon: 'Files'
    }
  },
  {
    path: '/voucher-query',
    name: 'voucher-query',
    component: () => import('../views/VoucherQueryView.vue'),
    meta: {
      title: '凭证查询',
      icon: 'Document'
    }
  },
  {
    path: '/quick-query',
    name: 'quick-query',
    component: QuickQueryView,
    meta: {
      title: '快速查询',
      icon: 'Search'
    }
  },
  {
    path: '/quick-fill',
    name: 'quick-fill',
    component: QuickFill,
    meta: {
      title: '速填模板',
      icon: 'EditPen'
    }
  },

  {
    path: '/settings',
    name: 'settings',
    component: Settings,
    meta: {
      title: '系统设置',
      icon: 'Setting'
    }
  }
  ],
})

export default router